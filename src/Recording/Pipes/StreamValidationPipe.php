<?php

declare(strict_types=1);

namespace LiveStream\Recording\Pipes;

use Closure;
use LiveStream\Recording\PendingRecorder;
use LiveStream\Exceptions\StreamUnavailableException;
use LiveStream\Exceptions\StreamValidationException;

/**
 * 流验证中间件
 * 
 * 负责验证流URL的可访问性和有效性，支持智能降级和重试机制
 */
class StreamValidationPipe
{
    /**
     * 处理流验证
     * 
     * @param PendingRecorder $pendingRecorder 待录制对象
     * @param Closure $next 下一个中间件
     * @return mixed
     * @throws StreamUnavailableException 当流不可用时
     * @throws StreamValidationException 当流格式无效时
     */
    public function handle(PendingRecorder $pendingRecorder, Closure $next)
    {
        $config = $pendingRecorder->recordrConnector()->config();

        // 检查是否启用了流验证（默认禁用，避免影响现有测试）
        try {
            $isEnabled = $config->isStreamValidationEnabled();
        } catch (\Throwable $e) {
            // 如果方法不存在或调用失败，默认禁用验证
            return $next($pendingRecorder);
        }

        if (!$isEnabled) {
            return $next($pendingRecorder);
        }

        // 获取最大重试次数
        try {
            $maxRetries = $config->getMaxValidationRetries();
        } catch (\Throwable $e) {
            $maxRetries = 2;
        }

        // 确定首选的流类型
        try {
            $preferredStreamType = $config->getPreferredStreamType();
        } catch (\Throwable $e) {
            $preferredStreamType = 'hls';
        }

        // 尝试验证流URL
        $validatedUrl = $this->validateStreamWithRetry($pendingRecorder, $preferredStreamType, $maxRetries);

        if ($validatedUrl === null) {
            // 如果首选类型失败，尝试备用类型
            $fallbackType = $preferredStreamType === 'hls' ? 'flv' : 'hls';
            $validatedUrl = $this->validateStreamWithRetry($pendingRecorder, $fallbackType, $maxRetries);
        }

        if ($validatedUrl === null) {
            throw new StreamUnavailableException('No valid stream URL available');
        }

        return $next($pendingRecorder);
    }

    /**
     * 带重试的流验证
     * 
     * @param PendingRecorder $pendingRecorder 待录制对象
     * @param string $streamType 流类型
     * @param int $maxRetries 最大重试次数
     * @return string|null 验证通过的流URL
     */
    private function validateStreamWithRetry(PendingRecorder $pendingRecorder, string $streamType, int $maxRetries): ?string
    {
        $attempt = 0;

        while ($attempt <= $maxRetries) {
            $url = $pendingRecorder->getValidatedStreamUrl($streamType);

            if ($url === null) {
                // 没有更多清晰度可尝试
                break;
            }

            // 验证HTTP连接
            if ($this->validateStreamConnection($pendingRecorder, $url)) {
                // 验证流格式
                if ($this->validateStreamFormat($pendingRecorder, $url, $streamType)) {
                    return $url;
                }
            }

            // 验证失败，刷新流URL以触发降级
            if ($attempt < $maxRetries) {
                $pendingRecorder->refreshStreamUrls();
            }

            $attempt++;
        }

        return null;
    }

    /**
     * 验证流连接
     * 
     * @param PendingRecorder $pendingRecorder 待录制对象
     * @param string $url 流URL
     * @return bool 连接是否成功
     */
    private function validateStreamConnection(PendingRecorder $pendingRecorder, string $url): bool
    {
        // 如果PendingRecorder有自定义的连接验证方法，使用它
        if (method_exists($pendingRecorder, 'validateStreamConnection')) {
            return $pendingRecorder->validateStreamConnection($url);
        }

        // 默认的HTTP连接验证
        return $this->performHttpConnectionTest($url);
    }

    /**
     * 验证流格式
     * 
     * @param PendingRecorder $pendingRecorder 待录制对象
     * @param string $url 流URL
     * @param string $streamType 流类型
     * @return bool 格式是否有效
     */
    private function validateStreamFormat(PendingRecorder $pendingRecorder, string $url, string $streamType): bool
    {
        // 如果PendingRecorder有自定义的格式验证方法，使用它
        if (method_exists($pendingRecorder, 'validateStreamFormat')) {
            return $pendingRecorder->validateStreamFormat($url, $streamType);
        }

        // 默认的格式验证
        return $this->performStreamFormatValidation($url, $streamType);
    }

    /**
     * 执行HTTP连接测试
     * 
     * @param string $url 流URL
     * @return bool 连接是否成功
     */
    private function performHttpConnectionTest(string $url): bool
    {
        try {
            $context = stream_context_create([
                'http' => [
                    'method' => 'HEAD',
                    'timeout' => 10,
                    'user_agent' => 'LiveStream/1.0',
                    'follow_location' => true,
                    'max_redirects' => 3,
                ],
            ]);

            $headers = @get_headers($url, false, $context);

            if ($headers === false) {
                return false;
            }

            // 检查HTTP状态码
            $statusLine = $headers[0] ?? '';
            return strpos($statusLine, '200') !== false || strpos($statusLine, '302') !== false;
        } catch (\Throwable $e) {
            return false;
        }
    }

    /**
     * 执行流格式验证
     * 
     * @param string $url 流URL
     * @param string $streamType 流类型
     * @return bool 格式是否有效
     */
    private function performStreamFormatValidation(string $url, string $streamType): bool
    {
        try {
            // 基本的URL格式检查
            if (!filter_var($url, FILTER_VALIDATE_URL)) {
                return false;
            }

            // 检查文件扩展名
            $extension = strtolower(pathinfo(parse_url($url, PHP_URL_PATH), PATHINFO_EXTENSION));

            switch ($streamType) {
                case 'hls':
                    return $extension === 'm3u8' || strpos($url, '.m3u8') !== false;
                case 'flv':
                    return $extension === 'flv' || strpos($url, '.flv') !== false;
                default:
                    return true; // 未知类型，假设有效
            }
        } catch (\Throwable $e) {
            return false;
        }
    }
}
